import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // استرجاع كل المرتجعات من قاعدة البيانات، مرتبة تنازلياً
    const returns = await prisma.return.findMany({
      orderBy: { id: 'desc' }
    });
    return NextResponse.json(returns);
  } catch (error) {
    console.error('Failed to fetch returns:', error);
    return NextResponse.json({ error: 'Failed to fetch returns' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newReturn = await request.json();
    
    // البحث عن أعلى معرف لإنشاء رقم تسلسلي جديد
    const maxIdRecord = await prisma.return.findFirst({
      orderBy: { id: 'desc' }
    });
    
    const newId = (maxIdRecord?.id || 0) + 1;
    
    // إنشاء المرتجع في قاعدة البيانات
    const returnRecord = await prisma.return.create({
      data: {
        roNumber: `RO-${newId}`,
        opReturnNumber: newReturn.opReturnNumber,
        date: newReturn.date,
        saleId: newReturn.saleId,
        soNumber: newReturn.soNumber,
        clientName: newReturn.clientName,
        warehouseName: newReturn.warehouseName,
        items: newReturn.items, // JSON field
        notes: newReturn.notes || '',
        status: newReturn.status || 'معلق',
        processedBy: newReturn.processedBy,
        processedDate: newReturn.processedDate,
        employeeName: newReturn.employeeName,
        attachments: newReturn.attachments ? JSON.stringify(newReturn.attachments) : null
      }
    });
    
    // تحديث حالة الأجهزة
    if (newReturn.items && Array.isArray(newReturn.items)) {
      for (const item of newReturn.items) {
        try {
          // تحديد الحالة الجديدة حسب سبب الإرجاع
          let newStatus = 'متاح للبيع';
          if (item.returnReason === 'خلل مصنعي') {
            newStatus = 'بانتظار إرسال للصيانة';
          } else if (item.returnReason === 'سبب آخر') {
            newStatus = 'بانتظار إرسال للصيانة';
          }
          
          await prisma.device.update({
            where: { id: item.deviceId },
            data: { status: newStatus }
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId}:`, deviceError);
        }
      }
    }
    
    return NextResponse.json(returnRecord, { status: 201 });
  } catch (error) {
    console.error('Failed to create return:', error);
    return NextResponse.json({ error: 'Failed to create return' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedReturn = await request.json();
    
    // التحقق من وجود المرتجع
    const existingReturn = await prisma.return.findUnique({
      where: { id: updatedReturn.id }
    });
    
    if (!existingReturn) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }
    
    // تحديث المرتجع
    const returnRecord = await prisma.return.update({
      where: { id: updatedReturn.id },
      data: {
        opReturnNumber: updatedReturn.opReturnNumber,
        date: updatedReturn.date,
        saleId: updatedReturn.saleId,
        soNumber: updatedReturn.soNumber,
        clientName: updatedReturn.clientName,
        warehouseName: updatedReturn.warehouseName,
        items: updatedReturn.items, // JSON field
        notes: updatedReturn.notes || '',
        status: updatedReturn.status || 'معلق',
        processedBy: updatedReturn.processedBy,
        processedDate: updatedReturn.processedDate,
        employeeName: updatedReturn.employeeName,
        attachments: updatedReturn.attachments ? JSON.stringify(updatedReturn.attachments) : null
      }
    });
    
    return NextResponse.json(returnRecord);
  } catch (error) {
    console.error('Failed to update return:', error);
    return NextResponse.json({ error: 'Failed to update return' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
    // التحقق من وجود المرتجع
    const existingReturn = await prisma.return.findUnique({
      where: { id }
    });
    
    if (!existingReturn) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }
    
    // إرجاع حالة الأجهزة إلى "مباع" قبل الحذف
    if (existingReturn.items && Array.isArray(existingReturn.items)) {
      for (const item of existingReturn.items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId },
            data: { status: 'مباع' }
          });
        } catch (deviceError) {
          console.error(`Failed to revert device ${item.deviceId}:`, deviceError);
        }
      }
    }
    
    // حذف المرتجع
    await prisma.return.delete({
      where: { id }
    });
    
    return NextResponse.json({ message: 'Return deleted successfully' });
  } catch (error) {
    console.error('Failed to delete return:', error);
    return NextResponse.json({ error: 'Failed to delete return' }, { status: 500 });
  }
}
