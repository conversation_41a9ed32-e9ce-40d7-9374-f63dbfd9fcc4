import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const supplyOrders = await prisma.supplyOrder.findMany({
      orderBy: { id: 'desc' }
    });
    return NextResponse.json(supplyOrders);
  } catch (error) {
    console.error('Failed to fetch supply orders:', error);
    return NextResponse.json({ error: 'Failed to fetch supply orders' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newOrder = await request.json();
    
    // Find the highest id to generate a new supplyOrderId
    const maxIdRecord = await prisma.supplyOrder.findFirst({
      orderBy: { id: 'desc' }
    });
    
    const newId = (maxIdRecord?.id || 0) + 1;
    
    // Create the order in the database
    const order = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: `SUP-${newId}`,
        supplierId: newOrder.supplierId,
        invoiceNumber: newOrder.invoiceNumber,
        supplyDate: newOrder.supplyDate,
        warehouseId: newOrder.warehouseId,
        employeeName: newOrder.employeeName,
        items: newOrder.items, // JSON field
        notes: newOrder.notes || '',
        invoiceFileName: newOrder.invoiceFileName,
        referenceNumber: newOrder.referenceNumber,
        status: newOrder.status || 'completed'
      }
    });
    
    // Add devices to database if needed
    if (newOrder.items && Array.isArray(newOrder.items)) {
      for (const item of newOrder.items) {
        try {
          // Check if device already exists
          const existingDevice = await prisma.device.findUnique({
            where: { id: item.imei }
          });
          
          if (!existingDevice) {
            await prisma.device.create({
              data: {
                id: item.imei,
                model: `${item.manufacturer} ${item.model}`,
                status: 'متاح للبيع',
                storage: 'N/A',
                price: 0,
                condition: item.condition,
                warehouseId: newOrder.warehouseId,
                supplierId: newOrder.supplierId
              }
            });
          }
        } catch (deviceError) {
          console.error('Failed to create device:', deviceError);
        }
      }
    }
    
    return NextResponse.json(order, { status: 201 });
  } catch (error) {
    console.error('Failed to create supply order:', error);
    return NextResponse.json({ error: 'Failed to create supply order' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedOrder = await request.json();
    
    // Check if order exists
    const existingOrder = await prisma.supplyOrder.findUnique({
      where: { id: updatedOrder.id }
    });
    
    if (!existingOrder) {
      return NextResponse.json({ error: 'Supply order not found' }, { status: 404 });
    }
    
    // Update the order
    const order = await prisma.supplyOrder.update({
      where: { id: updatedOrder.id },
      data: {
        supplierId: updatedOrder.supplierId,
        invoiceNumber: updatedOrder.invoiceNumber,
        supplyDate: updatedOrder.supplyDate,
        warehouseId: updatedOrder.warehouseId,
        employeeName: updatedOrder.employeeName,
        items: updatedOrder.items, // JSON field
        notes: updatedOrder.notes || '',
        invoiceFileName: updatedOrder.invoiceFileName,
        referenceNumber: updatedOrder.referenceNumber,
        status: updatedOrder.status || 'completed'
      }
    });
    
    // Handle devices updates if needed
    // Implementation depends on business logic
    
    return NextResponse.json(order);
  } catch (error) {
    console.error('Failed to update supply order:', error);
    return NextResponse.json({ error: 'Failed to update supply order' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
    // Check if order exists
    const existingOrder = await prisma.supplyOrder.findUnique({
      where: { id }
    });
    
    if (!existingOrder) {
      return NextResponse.json({ error: 'Supply order not found' }, { status: 404 });
    }
    
    // Delete the order
    await prisma.supplyOrder.delete({
      where: { id }
    });
    
    return NextResponse.json({ message: 'Supply order deleted successfully' });
  } catch (error) {
    console.error('Failed to delete supply order:', error);
    return NextResponse.json({ error: 'Failed to delete supply order' }, { status: 500 });
  }
}
