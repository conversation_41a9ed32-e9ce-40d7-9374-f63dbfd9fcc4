import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const sales = await prisma.sale.findMany({
      orderBy: { id: 'desc' }
    });

    // تحويل حقول JSON من strings إلى objects
    const processedSales = sales.map((sale: any) => ({
      ...sale,
      items: sale.items ?
        (typeof sale.items === 'string' ?
          JSON.parse(sale.items) : sale.items) : [],
      attachments: sale.attachments ?
        (typeof sale.attachments === 'string' ?
          JSON.parse(sale.attachments) : sale.attachments) : null,
    }));

    return NextResponse.json(processedSales);
  } catch (error) {
    console.error('Failed to fetch sales:', error);
    return NextResponse.json({ error: 'Failed to fetch sales' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newSale = await request.json();
    
    // Find the highest id to generate a new soNumber
    const maxIdRecord = await prisma.sale.findFirst({
      orderBy: { id: 'desc' }
    });
    
    const newId = (maxIdRecord?.id || 0) + 1;
    
    // Create the sale in the database
    const sale = await prisma.sale.create({
      data: {
        soNumber: `SO-${newId}`,
        opNumber: newSale.opNumber,
        date: newSale.date,
        clientName: newSale.clientName,
        warehouseName: newSale.warehouseName,
        items: newSale.items, // JSON field
        notes: newSale.notes || '',
        warrantyPeriod: newSale.warrantyPeriod,
        employeeName: newSale.employeeName,
        attachments: newSale.attachments ? JSON.stringify(newSale.attachments) : null
      }
    });
    
    // Update device statuses
    if (newSale.items && Array.isArray(newSale.items)) {
      for (const item of newSale.items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId },
            data: { status: 'مباع' }
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId}:`, deviceError);
        }
      }
    }

    // معالجة حقول JSON قبل الإرسال
    const processedSale = {
      ...sale,
      items: sale.items ?
        (typeof sale.items === 'string' ?
          JSON.parse(sale.items) : sale.items) : [],
      attachments: sale.attachments ?
        (typeof sale.attachments === 'string' ?
          JSON.parse(sale.attachments) : sale.attachments) : null,
    };

    return NextResponse.json(processedSale, { status: 201 });
  } catch (error) {
    console.error('Failed to create sale:', error);
    return NextResponse.json({ error: 'Failed to create sale' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedSale = await request.json();
    
    // Check if sale exists
    const existingSale = await prisma.sale.findUnique({
      where: { id: updatedSale.id }
    });
    
    if (!existingSale) {
      return NextResponse.json({ error: 'Sale not found' }, { status: 404 });
    }
    
    // Get the old items to check for removed devices
    const oldItems = existingSale.items as any;
    const newItemIds = updatedSale.items.map((item: any) => item.deviceId);
    
    // Find devices that were removed from the sale
    const removedDeviceIds = oldItems
      .filter((item: any) => !newItemIds.includes(item.deviceId))
      .map((item: any) => item.deviceId);
    
    // Reset status for removed devices
    for (const deviceId of removedDeviceIds) {
      try {
        await prisma.device.update({
          where: { id: deviceId },
          data: { status: 'متاح للبيع' }
        });
      } catch (deviceError) {
        console.error(`Failed to update device ${deviceId}:`, deviceError);
      }
    }
    
    // Set status for new devices
    for (const item of updatedSale.items) {
      try {
        await prisma.device.update({
          where: { id: item.deviceId },
          data: { status: 'مباع' }
        });
      } catch (deviceError) {
        console.error(`Failed to update device ${item.deviceId}:`, deviceError);
      }
    }
    
    // Update the sale
    const sale = await prisma.sale.update({
      where: { id: updatedSale.id },
      data: {
        opNumber: updatedSale.opNumber,
        date: updatedSale.date,
        clientName: updatedSale.clientName,
        warehouseName: updatedSale.warehouseName,
        items: updatedSale.items,
        notes: updatedSale.notes || '',
        warrantyPeriod: updatedSale.warrantyPeriod,
        employeeName: updatedSale.employeeName,
        attachments: updatedSale.attachments ? JSON.stringify(updatedSale.attachments) : null
      }
    });

    // معالجة حقول JSON قبل الإرسال
    const processedSale = {
      ...sale,
      items: sale.items ?
        (typeof sale.items === 'string' ?
          JSON.parse(sale.items) : sale.items) : [],
      attachments: sale.attachments ?
        (typeof sale.attachments === 'string' ?
          JSON.parse(sale.attachments) : sale.attachments) : null,
    };

    return NextResponse.json(processedSale);
  } catch (error) {
    console.error('Failed to update sale:', error);
    return NextResponse.json({ error: 'Failed to update sale' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
    // Check if sale exists
    const existingSale = await prisma.sale.findUnique({
      where: { id }
    });
    
    if (!existingSale) {
      return NextResponse.json({ error: 'Sale not found' }, { status: 404 });
    }
    
    // Reset device statuses
    const items = existingSale.items as any;
    if (items && Array.isArray(items)) {
      for (const item of items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId },
            data: { status: 'متاح للبيع' }
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId}:`, deviceError);
        }
      }
    }
    
    // Delete the sale
    await prisma.sale.delete({
      where: { id }
    });
    
    return NextResponse.json({ message: 'Sale deleted successfully' });
  } catch (error) {
    console.error('Failed to delete sale:', error);
    return NextResponse.json({ error: 'Failed to delete sale' }, { status: 500 });
  }
}
