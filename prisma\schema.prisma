// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id         Int     @id @default(autoincrement())
  email      String  @unique
  name       String?
  username   String? @default("user")
  role       String? @default("user")
  phone      String? @default("")
  photo      String? @default("")
  status     String? @default("Active")
  lastLogin  String?
  permissions Json?   // للصلاحيات المخزنة كـ JSON
  posts      Post[]
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  author    User    @relation(fields: [authorId], references: [id])
  authorId  Int
}

model SystemSetting {
  id              Int      @id @default(1)
  logoUrl         String   @default("")
  companyNameAr   String   @default("")
  companyNameEn   String   @default("")
  addressAr       String   @default("")
  addressEn       String   @default("")
  phone           String   @default("")
  email           String   @default("")
  website         String   @default("")
  footerTextAr    String   @default("")
  footerTextEn    String   @default("")
  updatedAt       DateTime @updatedAt @default(now())
  createdAt       DateTime @default(now())
}

model AuditLog {
  id        Int      @id @default(autoincrement())
  timestamp DateTime @default(now())
  userId    Int
  username  String
  operation String
  details   String
}

model SupplyOrder {
  id             Int      @id @default(autoincrement())
  supplyOrderId  String   @unique
  supplierId     Int
  invoiceNumber  String
  supplyDate     String
  warehouseId    Int
  employeeName   String
  items          Json     // سيتم تخزين العناصر كـ JSON
  notes          String?
  invoiceFileName String?
  referenceNumber String?
  createdAt      DateTime @default(now())
  status         String?  @default("completed")
}

model Sale {
  id             Int      @id @default(autoincrement())
  soNumber       String   @unique
  opNumber       String
  date           String
  clientName     String
  warehouseName  String
  items          Json     // سيتم تخزين العناصر كـ JSON
  notes          String?
  warrantyPeriod String
  employeeName   String
  createdAt      DateTime @default(now())
  attachments    String?
}

model Device {
  id          String   @id
  model       String
  status      String
  storage     String
  price       Float
  condition   String
  warehouseId Int?
  supplierId  Int?
  dateAdded   DateTime @default(now())
  replacementInfo Json?  // معلومات الاستبدال كـ JSON
}

model Warehouse {
  id       Int    @id @default(autoincrement())
  name     String
  type     String
  location String
}
