import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const evaluations = await prisma.evaluationOrder.findMany({
      orderBy: { id: 'desc' }
    });
    
    // تحويل حقول JSON من strings إلى objects
    const processedEvaluations = evaluations.map((evaluation: any) => ({
      ...evaluation,
      items: evaluation.items ? 
        (typeof evaluation.items === 'string' ? 
          JSON.parse(evaluation.items) : evaluation.items) : [],
    }));
    
    return NextResponse.json(processedEvaluations);
  } catch (error) {
    console.error('Failed to fetch evaluations:', error);
    return NextResponse.json({ error: 'Failed to fetch evaluations' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newEvaluation = await request.json();
    
    // Basic validation
    if (!newEvaluation.orderId || !newEvaluation.employeeName) {
      return NextResponse.json(
        { error: 'Order ID and employee name are required' },
        { status: 400 },
      );
    }

    // Check if evaluation order already exists
    const existingEvaluation = await prisma.evaluationOrder.findUnique({
      where: { orderId: newEvaluation.orderId }
    });

    if (existingEvaluation) {
      return NextResponse.json(
        { error: 'Evaluation order with this ID already exists' },
        { status: 400 },
      );
    }

    // Create the evaluation order in the database
    const evaluation = await prisma.evaluationOrder.create({
      data: {
        orderId: newEvaluation.orderId,
        employeeName: newEvaluation.employeeName,
        date: newEvaluation.date || new Date().toISOString(),
        items: newEvaluation.items ? JSON.stringify(newEvaluation.items) : JSON.stringify([]),
        notes: newEvaluation.notes || null,
        status: newEvaluation.status || 'معلق',
        acknowledgedBy: newEvaluation.acknowledgedBy || null,
        acknowledgedDate: newEvaluation.acknowledgedDate || null,
        warehouseName: newEvaluation.warehouseName || null,
      }
    });

    // معالجة حقول JSON قبل الإرسال
    const processedEvaluation = {
      ...evaluation,
      items: evaluation.items ? 
        (typeof evaluation.items === 'string' ? 
          JSON.parse(evaluation.items) : evaluation.items) : [],
    };

    return NextResponse.json(processedEvaluation, { status: 201 });
  } catch (error) {
    console.error('Failed to create evaluation:', error);
    return NextResponse.json({ error: 'Failed to create evaluation' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedEvaluation = await request.json();
    
    // Check if evaluation exists
    const existingEvaluation = await prisma.evaluationOrder.findUnique({
      where: { id: updatedEvaluation.id }
    });
    
    if (!existingEvaluation) {
      return NextResponse.json(
        { error: 'Evaluation not found' },
        { status: 404 },
      );
    }

    // Update the evaluation
    const evaluation = await prisma.evaluationOrder.update({
      where: { id: updatedEvaluation.id },
      data: {
        orderId: updatedEvaluation.orderId,
        employeeName: updatedEvaluation.employeeName,
        date: updatedEvaluation.date,
        items: updatedEvaluation.items ? JSON.stringify(updatedEvaluation.items) : JSON.stringify([]),
        notes: updatedEvaluation.notes,
        status: updatedEvaluation.status,
        acknowledgedBy: updatedEvaluation.acknowledgedBy,
        acknowledgedDate: updatedEvaluation.acknowledgedDate,
        warehouseName: updatedEvaluation.warehouseName,
      }
    });
    
    // معالجة حقول JSON قبل الإرسال
    const processedEvaluation = {
      ...evaluation,
      items: evaluation.items ? 
        (typeof evaluation.items === 'string' ? 
          JSON.parse(evaluation.items) : evaluation.items) : [],
    };
    
    return NextResponse.json(processedEvaluation);
  } catch (error) {
    console.error('Failed to update evaluation:', error);
    return NextResponse.json({ error: 'Failed to update evaluation' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
    // Check if evaluation exists
    const existingEvaluation = await prisma.evaluationOrder.findUnique({
      where: { id: parseInt(id) }
    });
    
    if (!existingEvaluation) {
      return NextResponse.json(
        { error: 'Evaluation not found' },
        { status: 404 },
      );
    }

    // Delete the evaluation
    await prisma.evaluationOrder.delete({
      where: { id: parseInt(id) }
    });
    
    return NextResponse.json({ message: 'Evaluation deleted successfully' });
  } catch (error) {
    console.error('Failed to delete evaluation:', error);
    return NextResponse.json({ error: 'Failed to delete evaluation' }, { status: 500 });
  }
}
