import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      orderBy: { id: 'desc' }
    });
    
    // تحويل حقول JSON من strings إلى objects
    const processedOrders = maintenanceOrders.map((order: any) => ({
      ...order,
      items: order.items ? 
        (typeof order.items === 'string' ? 
          JSON.parse(order.items) : order.items) : [],
    }));
    
    return NextResponse.json(processedOrders);
  } catch (error) {
    console.error('Failed to fetch maintenance orders:', error);
    return NextResponse.json({ error: 'Failed to fetch maintenance orders' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newOrder = await request.json();
    
    // Basic validation
    if (!newOrder.orderNumber || !newOrder.employeeName) {
      return NextResponse.json(
        { error: 'Order number and employee name are required' },
        { status: 400 },
      );
    }

    // Check if maintenance order already exists
    const existingOrder = await prisma.maintenanceOrder.findUnique({
      where: { orderNumber: newOrder.orderNumber }
    });

    if (existingOrder) {
      return NextResponse.json(
        { error: 'Maintenance order with this number already exists' },
        { status: 400 },
      );
    }

    // Create the maintenance order in the database
    const order = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: newOrder.orderNumber,
        referenceNumber: newOrder.referenceNumber || null,
        date: newOrder.date || new Date().toISOString(),
        employeeName: newOrder.employeeName,
        maintenanceEmployeeName: newOrder.maintenanceEmployeeName || null,
        items: newOrder.items ? JSON.stringify(newOrder.items) : JSON.stringify([]),
        notes: newOrder.notes || null,
        status: newOrder.status || 'wip',
        source: newOrder.source || 'warehouse',
        attachmentName: newOrder.attachmentName || null,
      }
    });

    // Update device statuses if items are provided
    if (newOrder.items && Array.isArray(newOrder.items)) {
      for (const item of newOrder.items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId || item.id },
            data: { status: 'بانتظار استلام في الصيانة' }
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId || item.id}:`, deviceError);
        }
      }
    }

    // معالجة حقول JSON قبل الإرسال
    const processedOrder = {
      ...order,
      items: order.items ? 
        (typeof order.items === 'string' ? 
          JSON.parse(order.items) : order.items) : [],
    };

    return NextResponse.json(processedOrder, { status: 201 });
  } catch (error) {
    console.error('Failed to create maintenance order:', error);
    return NextResponse.json({ error: 'Failed to create maintenance order' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedOrder = await request.json();
    
    // Check if maintenance order exists
    const existingOrder = await prisma.maintenanceOrder.findUnique({
      where: { id: updatedOrder.id }
    });
    
    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Maintenance order not found' },
        { status: 404 },
      );
    }

    // Update the maintenance order
    const order = await prisma.maintenanceOrder.update({
      where: { id: updatedOrder.id },
      data: {
        orderNumber: updatedOrder.orderNumber,
        referenceNumber: updatedOrder.referenceNumber,
        date: updatedOrder.date,
        employeeName: updatedOrder.employeeName,
        maintenanceEmployeeName: updatedOrder.maintenanceEmployeeName,
        items: updatedOrder.items ? JSON.stringify(updatedOrder.items) : JSON.stringify([]),
        notes: updatedOrder.notes,
        status: updatedOrder.status,
        source: updatedOrder.source,
        attachmentName: updatedOrder.attachmentName,
      }
    });
    
    // معالجة حقول JSON قبل الإرسال
    const processedOrder = {
      ...order,
      items: order.items ? 
        (typeof order.items === 'string' ? 
          JSON.parse(order.items) : order.items) : [],
    };
    
    return NextResponse.json(processedOrder);
  } catch (error) {
    console.error('Failed to update maintenance order:', error);
    return NextResponse.json({ error: 'Failed to update maintenance order' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

    // Check if maintenance order exists
    const existingOrder = await prisma.maintenanceOrder.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Maintenance order not found' },
        { status: 404 },
      );
    }

    // Parse items to update device statuses back
    let items = [];
    try {
      items = typeof existingOrder.items === 'string' ?
        JSON.parse(existingOrder.items) : existingOrder.items;
    } catch (error) {
      console.warn('Failed to parse items for device status update:', error);
    }

    // Update device statuses back to available
    if (Array.isArray(items)) {
      for (const item of items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId || item.id },
            data: { status: 'متاح للبيع' }
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId || item.id}:`, deviceError);
        }
      }
    }

    // Delete the maintenance order
    await prisma.maintenanceOrder.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'Maintenance order deleted successfully' });
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);
    return NextResponse.json({ error: 'Failed to delete maintenance order' }, { status: 500 });
  }
}
