import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

async function logAudit(
  operation: string,
  details: string,
  userId: number,
  username: string,
) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        username,
        operation,
        details,
      }
    });
  } catch (error) {
    console.error('Failed to log audit entry:', error);
  }
}

export async function GET() {
  try {
    const users = await prisma.user.findMany();

    // تحويل حقول JSON من strings إلى objects
    const processedUsers = users.map((user: any) => ({
      ...user,
      permissions: user.permissions ?
        (typeof user.permissions === 'string' ?
          JSON.parse(user.permissions) : user.permissions) : null,
      warehouseAccess: user.warehouseAccess ?
        (typeof user.warehouseAccess === 'string' ?
          JSON.parse(user.warehouseAccess) : user.warehouseAccess) : null,
    }));

    return NextResponse.json(processedUsers);
  } catch (error) {
    console.error('Failed to fetch users:', error);
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newUser = await request.json();

    // Basic validation
    if (!newUser.name || !newUser.email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 },
      );
    }

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: newUser.email }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 409 },
      );
    }

    // Create new user
    const user = await prisma.user.create({
      data: {
        name: newUser.name,
        email: newUser.email,
        username: newUser.username || 'user',
        role: newUser.role || 'user',
        phone: newUser.phone || '',
        photo: newUser.photo || '',
        status: newUser.status || 'Active',
        branchLocation: newUser.branchLocation || null,
        warehouseAccess: newUser.warehouseAccess || null,
        permissions: newUser.permissions || null,
      }
    });

    await logAudit(
      'User Added',
      `New user ${user.name} (${user.email}) added.`,
      user.id,
      user.name,
    );

    // معالجة حقول JSON قبل الإرسال
    const processedUser = {
      ...user,
      permissions: user.permissions ?
        (typeof user.permissions === 'string' ?
          JSON.parse(user.permissions) : user.permissions) : null,
      warehouseAccess: user.warehouseAccess ?
        (typeof user.warehouseAccess === 'string' ?
          JSON.parse(user.warehouseAccess) : user.warehouseAccess) : null,
    };

    return NextResponse.json(processedUser, { status: 201 });
  } catch (error) {
    console.error('Failed to create user:', error);
    return NextResponse.json({ error: 'Failed to create user' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedUser = await request.json();
    
    if (!updatedUser.id) {
      return NextResponse.json(
        { message: 'User ID is required' },
        { status: 400 },
      );
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: updatedUser.id }
    });

    if (!existingUser) {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }

    // Check if new email already exists (excluding current user)
    if (updatedUser.email && updatedUser.email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: updatedUser.email }
      });

      if (emailExists) {
        return NextResponse.json(
          { message: 'Email already exists' },
          { status: 409 },
        );
      }
    }

    // Update user
    const user = await prisma.user.update({
      where: { id: updatedUser.id },
      data: {
        name: updatedUser.name || existingUser.name,
        email: updatedUser.email || existingUser.email,
        username: updatedUser.username !== undefined ? updatedUser.username : existingUser.username,
        role: updatedUser.role !== undefined ? updatedUser.role : existingUser.role,
        phone: updatedUser.phone !== undefined ? updatedUser.phone : existingUser.phone,
        photo: updatedUser.photo !== undefined ? updatedUser.photo : existingUser.photo,
        status: updatedUser.status !== undefined ? updatedUser.status : existingUser.status,
        branchLocation: updatedUser.branchLocation !== undefined ? updatedUser.branchLocation : existingUser.branchLocation,
        warehouseAccess: updatedUser.warehouseAccess !== undefined ? updatedUser.warehouseAccess : existingUser.warehouseAccess,
        permissions: updatedUser.permissions !== undefined ? updatedUser.permissions : existingUser.permissions,
        lastLogin: updatedUser.lastLogin !== undefined ? updatedUser.lastLogin : existingUser.lastLogin,
      }
    });

    await logAudit(
      'User Updated',
      `User ${user.name} (${user.id}) updated.`,
      user.id,
      user.name,
    );

    // معالجة حقول JSON قبل الإرسال
    const processedUser = {
      ...user,
      permissions: user.permissions ?
        (typeof user.permissions === 'string' ?
          JSON.parse(user.permissions) : user.permissions) : null,
      warehouseAccess: user.warehouseAccess ?
        (typeof user.warehouseAccess === 'string' ?
          JSON.parse(user.warehouseAccess) : user.warehouseAccess) : null,
    };

    return NextResponse.json(processedUser);
  } catch (error) {
    console.error('Failed to update user:', error);
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'User ID is required' },
        { status: 400 },
      );
    }

    // Protect Super Admin (assuming Super Admin has id: 1)
    if (id === 1) {
      return NextResponse.json(
        { message: 'Cannot delete Super Admin' },
        { status: 403 },
      );
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id }
    });

    if (!existingUser) {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }

    // Delete user
    await prisma.user.delete({
      where: { id }
    });

    await logAudit(
      'User Deleted',
      `User ${existingUser.name} (${existingUser.id}) deleted.`,
      existingUser.id,
      existingUser.name,
    );

    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Failed to delete user:', error);
    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 });
  }
}
