import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // استرجاع كل الموردين من قاعدة البيانات، مرتبة تنازلياً
    const suppliers = await prisma.supplier.findMany({
      orderBy: { id: 'desc' }
    });
    return NextResponse.json(suppliers);
  } catch (error) {
    console.error('Failed to fetch suppliers:', error);
    return NextResponse.json({ error: 'Failed to fetch suppliers' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newSupplier = await request.json();
    
    // التحقق من البيانات المطلوبة
    if (!newSupplier.name || !newSupplier.email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود بريد إلكتروني مكرر
    const existingSupplier = await prisma.supplier.findUnique({
      where: { email: newSupplier.email }
    });

    if (existingSupplier) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 409 }
      );
    }
    
    // إنشاء المورد في قاعدة البيانات
    const supplier = await prisma.supplier.create({
      data: {
        name: newSupplier.name,
        phone: newSupplier.phone || '',
        email: newSupplier.email,
      }
    });
    
    return NextResponse.json(supplier, { status: 201 });
  } catch (error) {
    console.error('Failed to create supplier:', error);
    return NextResponse.json({ error: 'Failed to create supplier' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedSupplier = await request.json();
    
    // التحقق من وجود المورد
    const existingSupplier = await prisma.supplier.findUnique({
      where: { id: updatedSupplier.id }
    });
    
    if (!existingSupplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
    }

    // التحقق من عدم وجود بريد إلكتروني مكرر (باستثناء المورد الحالي)
    if (updatedSupplier.email && updatedSupplier.email !== existingSupplier.email) {
      const emailExists = await prisma.supplier.findUnique({
        where: { email: updatedSupplier.email }
      });

      if (emailExists) {
        return NextResponse.json(
          { error: 'Email already exists' },
          { status: 409 }
        );
      }
    }
    
    // تحديث المورد
    const supplier = await prisma.supplier.update({
      where: { id: updatedSupplier.id },
      data: {
        name: updatedSupplier.name,
        phone: updatedSupplier.phone || '',
        email: updatedSupplier.email,
      }
    });
    
    return NextResponse.json(supplier);
  } catch (error) {
    console.error('Failed to update supplier:', error);
    return NextResponse.json({ error: 'Failed to update supplier' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
    // التحقق من وجود المورد
    const existingSupplier = await prisma.supplier.findUnique({
      where: { id }
    });
    
    if (!existingSupplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
    }
    
    // فحص العلاقات قبل الحذف
    // فحص أوامر التوريد
    const relatedSupplyOrders = await prisma.supplyOrder.findMany({
      where: { supplierId: id }
    });

    const relatedOperations: string[] = [];
    if (relatedSupplyOrders.length > 0) {
      relatedOperations.push(`${relatedSupplyOrders.length} أمر توريد`);
    }

    if (relatedOperations.length > 0) {
      return NextResponse.json({
        error: 'Cannot delete supplier',
        reason: 'يوجد عمليات مرتبطة بهذا المورد',
        relatedOperations
      }, { status: 409 });
    }
    
    // حذف المورد
    await prisma.supplier.delete({
      where: { id }
    });
    
    return NextResponse.json({ message: 'Supplier deleted successfully' });
  } catch (error) {
    console.error('Failed to delete supplier:', error);
    return NextResponse.json({ error: 'Failed to delete supplier' }, { status: 500 });
  }
}
