import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const maintenanceReceipts = await prisma.maintenanceReceiptOrder.findMany({
      orderBy: { id: 'desc' }
    });
    
    // تحويل حقول JSON من strings إلى objects
    const processedReceipts = maintenanceReceipts.map((receipt: any) => ({
      ...receipt,
      items: receipt.items ? 
        (typeof receipt.items === 'string' ? 
          JSON.parse(receipt.items) : receipt.items) : [],
    }));
    
    return NextResponse.json(processedReceipts);
  } catch (error) {
    console.error('Failed to fetch maintenance receipts:', error);
    return NextResponse.json({ error: 'Failed to fetch maintenance receipts' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newReceipt = await request.json();
    
    // Basic validation
    if (!newReceipt.receiptNumber || !newReceipt.employeeName) {
      return NextResponse.json(
        { error: 'Receipt number and employee name are required' },
        { status: 400 },
      );
    }

    // Check if maintenance receipt already exists
    const existingReceipt = await prisma.maintenanceReceiptOrder.findUnique({
      where: { receiptNumber: newReceipt.receiptNumber }
    });

    if (existingReceipt) {
      return NextResponse.json(
        { error: 'Maintenance receipt with this number already exists' },
        { status: 400 },
      );
    }

    // Create the maintenance receipt in the database
    const receipt = await prisma.maintenanceReceiptOrder.create({
      data: {
        receiptNumber: newReceipt.receiptNumber,
        referenceNumber: newReceipt.referenceNumber || null,
        date: newReceipt.date || new Date().toISOString(),
        employeeName: newReceipt.employeeName,
        maintenanceEmployeeName: newReceipt.maintenanceEmployeeName || null,
        items: newReceipt.items ? JSON.stringify(newReceipt.items) : JSON.stringify([]),
        notes: newReceipt.notes || null,
        status: newReceipt.status || 'completed',
        attachmentName: newReceipt.attachmentName || null,
      }
    });

    // Update device statuses if items are provided
    if (newReceipt.items && Array.isArray(newReceipt.items)) {
      for (const item of newReceipt.items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId || item.id },
            data: { status: 'متاح للبيع' }
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId || item.id}:`, deviceError);
        }
      }
    }

    // معالجة حقول JSON قبل الإرسال
    const processedReceipt = {
      ...receipt,
      items: receipt.items ? 
        (typeof receipt.items === 'string' ? 
          JSON.parse(receipt.items) : receipt.items) : [],
    };

    return NextResponse.json(processedReceipt, { status: 201 });
  } catch (error) {
    console.error('Failed to create maintenance receipt:', error);
    return NextResponse.json({ error: 'Failed to create maintenance receipt' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedReceipt = await request.json();
    
    // Check if maintenance receipt exists
    const existingReceipt = await prisma.maintenanceReceiptOrder.findUnique({
      where: { id: updatedReceipt.id }
    });
    
    if (!existingReceipt) {
      return NextResponse.json(
        { error: 'Maintenance receipt not found' },
        { status: 404 },
      );
    }

    // Update the maintenance receipt
    const receipt = await prisma.maintenanceReceiptOrder.update({
      where: { id: updatedReceipt.id },
      data: {
        receiptNumber: updatedReceipt.receiptNumber,
        referenceNumber: updatedReceipt.referenceNumber,
        date: updatedReceipt.date,
        employeeName: updatedReceipt.employeeName,
        maintenanceEmployeeName: updatedReceipt.maintenanceEmployeeName,
        items: updatedReceipt.items ? JSON.stringify(updatedReceipt.items) : JSON.stringify([]),
        notes: updatedReceipt.notes,
        status: updatedReceipt.status,
        attachmentName: updatedReceipt.attachmentName,
      }
    });
    
    // معالجة حقول JSON قبل الإرسال
    const processedReceipt = {
      ...receipt,
      items: receipt.items ? 
        (typeof receipt.items === 'string' ? 
          JSON.parse(receipt.items) : receipt.items) : [],
    };
    
    return NextResponse.json(processedReceipt);
  } catch (error) {
    console.error('Failed to update maintenance receipt:', error);
    return NextResponse.json({ error: 'Failed to update maintenance receipt' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

    // Check if maintenance receipt exists
    const existingReceipt = await prisma.maintenanceReceiptOrder.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingReceipt) {
      return NextResponse.json(
        { error: 'Maintenance receipt not found' },
        { status: 404 },
      );
    }

    // Parse items to update device statuses back
    let items = [];
    try {
      items = typeof existingReceipt.items === 'string' ?
        JSON.parse(existingReceipt.items) : existingReceipt.items;
    } catch (error) {
      console.warn('Failed to parse items for device status update:', error);
    }

    // Update device statuses back to maintenance
    if (Array.isArray(items)) {
      for (const item of items) {
        try {
          await prisma.device.update({
            where: { id: item.deviceId || item.id },
            data: { status: 'قيد الإصلاح' }
          });
        } catch (deviceError) {
          console.error(`Failed to update device ${item.deviceId || item.id}:`, deviceError);
        }
      }
    }

    // Delete the maintenance receipt
    await prisma.maintenanceReceiptOrder.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: 'Maintenance receipt deleted successfully' });
  } catch (error) {
    console.error('Failed to delete maintenance receipt:', error);
    return NextResponse.json({ error: 'Failed to delete maintenance receipt' }, { status: 500 });
  }
}
