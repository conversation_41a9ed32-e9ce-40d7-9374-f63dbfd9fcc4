import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { AuditLog } from '@/lib/types';

export async function GET() {
  const auditLogs = await prisma.auditLog.findMany();
  return NextResponse.json(auditLogs);
}

export async function POST(request: Request) {
  const { userId, username, operation, details }: AuditLog = await request.json();
  const newLog = await prisma.auditLog.create({
    data: {
      userId,
      username,
      operation,
      details,
    },
  });
  return NextResponse.json(newLog, { status: 201 });
}
