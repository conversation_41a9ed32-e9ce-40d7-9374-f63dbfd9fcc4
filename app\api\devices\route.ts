import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const devices = await prisma.device.findMany();
    return NextResponse.json(devices);
  } catch (error) {
    console.error('Failed to fetch devices:', error);
    return NextResponse.json({ error: 'Failed to fetch devices' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newDevice = await request.json();
    
    // Basic validation
    if (!newDevice.id || !newDevice.model) {
      return NextResponse.json(
        { message: 'Device ID and model are required' },
        { status: 400 },
      );
    }

    // Check if device already exists
    const existingDevice = await prisma.device.findUnique({
      where: { id: newDevice.id }
    });

    if (existingDevice) {
      return NextResponse.json(
        { message: 'Device with this ID already exists' },
        { status: 409 },
      );
    }

    // Create new device
    const device = await prisma.device.create({
      data: {
        id: newDevice.id,
        model: newDevice.model,
        status: newDevice.status || 'متاح للبيع',
        storage: newDevice.storage || 'N/A',
        price: newDevice.price || 0,
        condition: newDevice.condition || 'جديد',
        warehouseId: newDevice.warehouseId,
        supplierId: newDevice.supplierId,
        dateAdded: new Date().toISOString(),
        replacementInfo: newDevice.replacementInfo
      }
    });

    return NextResponse.json(device, { status: 201 });
  } catch (error) {
    console.error('Failed to create device:', error);
    return NextResponse.json({ error: 'Failed to create device' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedDevice = await request.json();
    
    if (!updatedDevice.id) {
      return NextResponse.json(
        { message: 'Device ID is required' },
        { status: 400 },
      );
    }

    // Check if device exists
    const existingDevice = await prisma.device.findUnique({
      where: { id: updatedDevice.id }
    });

    if (!existingDevice) {
      return NextResponse.json({ message: 'Device not found' }, { status: 404 });
    }

    // Update device
    const device = await prisma.device.update({
      where: { id: updatedDevice.id },
      data: {
        model: updatedDevice.model || existingDevice.model,
        status: updatedDevice.status || existingDevice.status,
        storage: updatedDevice.storage || existingDevice.storage,
        price: updatedDevice.price !== undefined ? updatedDevice.price : existingDevice.price,
        condition: updatedDevice.condition || existingDevice.condition,
        warehouseId: updatedDevice.warehouseId,
        supplierId: updatedDevice.supplierId,
        replacementInfo: updatedDevice.replacementInfo
      }
    });

    return NextResponse.json(device);
  } catch (error) {
    console.error('Failed to update device:', error);
    return NextResponse.json({ error: 'Failed to update device' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'Device ID is required' },
        { status: 400 },
      );
    }

    // Check if device exists
    const existingDevice = await prisma.device.findUnique({
      where: { id }
    });

    if (!existingDevice) {
      return NextResponse.json({ message: 'Device not found' }, { status: 404 });
    }

    // Delete device
    await prisma.device.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Device deleted successfully' });
  } catch (error) {
    console.error('Failed to delete device:', error);
    return NextResponse.json({ error: 'Failed to delete device' }, { status: 500 });
  }
}
