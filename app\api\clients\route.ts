import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // استرجاع كل العملاء من قاعدة البيانات، مرتبة تنازلياً
    const clients = await prisma.client.findMany({
      orderBy: { id: 'desc' }
    });
    return NextResponse.json(clients);
  } catch (error) {
    console.error('Failed to fetch clients:', error);
    return NextResponse.json({ error: 'Failed to fetch clients' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newClient = await request.json();
    
    // التحقق من البيانات المطلوبة
    if (!newClient.name || !newClient.email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود بريد إلكتروني مكرر
    const existingClient = await prisma.client.findUnique({
      where: { email: newClient.email }
    });

    if (existingClient) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 409 }
      );
    }
    
    // إنشاء العميل في قاعدة البيانات
    const client = await prisma.client.create({
      data: {
        name: newClient.name,
        phone: newClient.phone || '',
        email: newClient.email,
      }
    });
    
    return NextResponse.json(client, { status: 201 });
  } catch (error) {
    console.error('Failed to create client:', error);
    return NextResponse.json({ error: 'Failed to create client' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedClient = await request.json();
    
    // التحقق من وجود العميل
    const existingClient = await prisma.client.findUnique({
      where: { id: updatedClient.id }
    });
    
    if (!existingClient) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // التحقق من عدم وجود بريد إلكتروني مكرر (باستثناء العميل الحالي)
    if (updatedClient.email && updatedClient.email !== existingClient.email) {
      const emailExists = await prisma.client.findUnique({
        where: { email: updatedClient.email }
      });

      if (emailExists) {
        return NextResponse.json(
          { error: 'Email already exists' },
          { status: 409 }
        );
      }
    }
    
    // تحديث العميل
    const client = await prisma.client.update({
      where: { id: updatedClient.id },
      data: {
        name: updatedClient.name,
        phone: updatedClient.phone || '',
        email: updatedClient.email,
      }
    });
    
    return NextResponse.json(client);
  } catch (error) {
    console.error('Failed to update client:', error);
    return NextResponse.json({ error: 'Failed to update client' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    
    // التحقق من وجود العميل
    const existingClient = await prisma.client.findUnique({
      where: { id }
    });
    
    if (!existingClient) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }
    
    // فحص العلاقات قبل الحذف
    // فحص المبيعات
    const relatedSales = await prisma.sale.findMany({
      where: { clientName: existingClient.name }
    });

    // فحص المرتجعات
    const relatedReturns = await prisma.return.findMany({
      where: { clientName: existingClient.name }
    });

    const relatedOperations: string[] = [];
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    if (relatedOperations.length > 0) {
      return NextResponse.json({
        error: 'Cannot delete client',
        reason: 'يوجد عمليات مرتبطة بهذا العميل',
        relatedOperations
      }, { status: 409 });
    }
    
    // حذف العميل
    await prisma.client.delete({
      where: { id }
    });
    
    return NextResponse.json({ message: 'Client deleted successfully' });
  } catch (error) {
    console.error('Failed to delete client:', error);
    return NextResponse.json({ error: 'Failed to delete client' }, { status: 500 });
  }
}
