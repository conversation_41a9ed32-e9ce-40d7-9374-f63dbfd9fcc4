import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const warehouses = await prisma.warehouse.findMany();
    return NextResponse.json(warehouses);
  } catch (error) {
    console.error('Failed to fetch warehouses:', error);
    return NextResponse.json({ error: 'Failed to fetch warehouses' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const newWarehouse = await request.json();
    
    // Basic validation
    if (!newWarehouse.name || !newWarehouse.type) {
      return NextResponse.json(
        { message: 'Warehouse name and type are required' },
        { status: 400 },
      );
    }

    // Create new warehouse
    const warehouse = await prisma.warehouse.create({
      data: {
        name: newWarehouse.name,
        type: newWarehouse.type,
        location: newWarehouse.location || '',
      }
    });

    return NextResponse.json(warehouse, { status: 201 });
  } catch (error) {
    console.error('Failed to create warehouse:', error);
    return NextResponse.json({ error: 'Failed to create warehouse' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const updatedWarehouse = await request.json();
    
    if (!updatedWarehouse.id) {
      return NextResponse.json(
        { message: 'Warehouse ID is required' },
        { status: 400 },
      );
    }

    // Check if warehouse exists
    const existingWarehouse = await prisma.warehouse.findUnique({
      where: { id: updatedWarehouse.id }
    });

    if (!existingWarehouse) {
      return NextResponse.json({ message: 'Warehouse not found' }, { status: 404 });
    }

    // Update warehouse
    const warehouse = await prisma.warehouse.update({
      where: { id: updatedWarehouse.id },
      data: {
        name: updatedWarehouse.name || existingWarehouse.name,
        type: updatedWarehouse.type || existingWarehouse.type,
        location: updatedWarehouse.location || existingWarehouse.location,
      }
    });

    return NextResponse.json(warehouse);
  } catch (error) {
    console.error('Failed to update warehouse:', error);
    return NextResponse.json({ error: 'Failed to update warehouse' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'Warehouse ID is required' },
        { status: 400 },
      );
    }

    // Check if warehouse exists
    const existingWarehouse = await prisma.warehouse.findUnique({
      where: { id }
    });

    if (!existingWarehouse) {
      return NextResponse.json({ message: 'Warehouse not found' }, { status: 404 });
    }

    // فحص العلاقات الشامل قبل الحذف
    const relatedOperations: string[] = [];

    // فحص الأجهزة في هذا المخزن
    const devicesInWarehouse = await prisma.device.count({
      where: { warehouseId: id }
    });
    if (devicesInWarehouse > 0) {
      relatedOperations.push(`${devicesInWarehouse} جهاز`);
    }

    // فحص المبيعات من هذا المخزن
    const relatedSales = await prisma.sale.findMany({
      where: { warehouseName: existingWarehouse.name }
    });
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }

    // فحص المرتجعات من هذا المخزن
    const relatedReturns = await prisma.return.findMany({
      where: { warehouseName: existingWarehouse.name }
    });
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    // فحص أوامر التوريد لهذا المخزن
    const relatedSupplyOrders = await prisma.supplyOrder.findMany({
      where: { warehouseId: id }
    });
    if (relatedSupplyOrders.length > 0) {
      relatedOperations.push(`${relatedSupplyOrders.length} أمر توريد`);
    }

    if (relatedOperations.length > 0) {
      return NextResponse.json({
        error: 'Cannot delete warehouse',
        reason: 'يوجد عمليات أو أجهزة مرتبطة بهذا المخزن',
        relatedOperations
      }, { status: 409 });
    }

    // Delete warehouse
    await prisma.warehouse.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Warehouse deleted successfully' });
  } catch (error) {
    console.error('Failed to delete warehouse:', error);
    return NextResponse.json({ error: 'Failed to delete warehouse' }, { status: 500 });
  }
}
